<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center_horizontal">

        <!-- Header -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="LiveKit Connection Info"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:layout_marginBottom="24dp" />

        <!-- Connection Status Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/cardBackground"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Connection Status"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/white"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/connectionStatusText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Disconnected"
                    android:textSize="16sp"
                    android:textColor="@color/red" />

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- Room Information Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/cardBackground"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Room Information"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/white"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/roomStateText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Room State: Unknown"
                    android:textSize="14sp"
                    android:textColor="@color/white"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/serverUrlText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Server: Not available"
                    android:textSize="14sp"
                    android:textColor="@color/white"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/participantInfoText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Participant: Not connected"
                    android:textSize="14sp"
                    android:textColor="@color/white"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/connectionQualityText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Connection Quality: Unknown"
                    android:textSize="14sp"
                    android:textColor="@color/white"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/tokenTtlText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Token TTL: Unknown"
                    android:textSize="14sp"
                    android:textColor="@color/white" />

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- Control Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center">

            <Button
                android:id="@+id/connectButton"
                android:layout_width="200dp"
                android:layout_height="wrap_content"
                android:text="Connect"
                android:textColor="@color/white"
                android:backgroundTint="@color/satiBotRed"
                android:layout_marginBottom="12dp" />

            <Button
                android:id="@+id/refreshTokenButton"
                android:layout_width="200dp"
                android:layout_height="wrap_content"
                android:text="Refresh Token"
                android:textColor="@color/white"
                android:backgroundTint="@color/satiBotRed" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
